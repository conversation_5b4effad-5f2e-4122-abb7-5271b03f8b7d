import pytest
import json
from unittest.mock import Mock, patch

COUNTER_REPORT = Mock()

# Mock S2A Properties
_request_context = {
    "authorizer": {
        "claims": json.dumps({"scopes": ["performance-analytics"]}),
        "principalId": json.dumps("some-principal-id"),
        "user": json.dumps({
            "userId": "test-user-id",
            "username": "test-user",
            "login": "test-login",
            "groups": []
        }),
        "account": json.dumps({"accountId": "test-account-id", "userPoolId": "some-user-pool"}),
    }
}


def setup_module(module):
    module.patcher = patch.dict(
        "sys.modules",
        {
            "counter_report": COUNTER_REPORT,
        },
    )
    module.patcher.start()


def teardown_module(module):
    module.patcher.stop()


@pytest.fixture(scope="session", autouse=True)
def s2a_properties_mock():
    from performance_analytics.utility.s2a_request_wrapper import CommonShare2ActProperties

    with patch("performance_analytics.fast_api.dependencies.s2a_properties.CommonShare2ActProperties") as event_mock:
        properties = CommonShare2ActProperties(_request_context)
        event_mock.return_value = properties
        yield event_mock


@pytest.fixture
def counter_report_performance():
    COUNTER_REPORT.get.return_value = {"counter_report": "counter-report-response"}
    COUNTER_REPORT.reset_mock()
    return COUNTER_REPORT


@pytest.fixture
def counter_report_event():
    def _counter_report_event(request):
        return {
            "version": "2.0",
            "routeKey": "GET /v1/performance-analytics/counter-report/v2/{line_id}/{machine_id}",
            "rawPath": "/v1/performance-analytics/counter-report/v2/test-line-id/test-machine-id",
            "rawQueryString": f"time_from={request[0]}&time_to={request[1]}" if request[0] and request[1] else "",
            "headers": {
                "accept": "application/json",
                "content-type": "application/json",
            },
            "queryStringParameters": {"time_from": request[0], "time_to": request[1]} if request[0] and request[1] else None,
            "requestContext": {
                "accountId": "test-account-id",
                "apiId": "test-api-id",
                "domainName": "test-domain.com",
                "http": {
                    "method": "GET",
                    "path": "/v1/performance-analytics/counter-report/v2/test-line-id/test-machine-id",
                    "protocol": "HTTP/1.1",
                    "sourceIp": "127.0.0.1",
                    "userAgent": "test-agent"
                },
                "requestId": "test-request-id",
                "routeKey": "GET /v1/performance-analytics/counter-report/v2/{line_id}/{machine_id}",
                "stage": "test",
                "time": "01/Jan/2021:00:00:00 +0000",
                "timeEpoch": *************,
                "authorizer": {
                    "claims": '{"scopes": ["performance-analytics"]}',
                    "user": '{"userId": "test-user-id", "username": "test-user", "login": "test-login", "groups": []}',
                    "account": '{"accountId": "test-account-id"}',
                },
            },
            "pathParameters": {
                "line_id": "test-line-id",
                "machine_id": "test-machine-id"
            },
            "isBase64Encoded": False,
        }

    return _counter_report_event


@pytest.fixture
def lambda_handler():
    """
    Unit under test
    """
    from src.lambda_function import lambda_handler

    return lambda_handler


def test_lambda_handler_call_counter_report(
    lambda_handler, counter_report_event, counter_report_performance
):
    """Test whether units-report is called as well as its result is returned."""
    report_event = counter_report_event(["*************", "*************"])
    actual_result = lambda_handler(report_event, "context")

    assert actual_result == {"counter_report": "counter-report-response"}
    counter_report_performance.get.assert_called_once_with(
        {
            "http_method": "GET",
            "feature_flags": ["performance-analytics"],
            "account": "test-account-id",
            "caller": "test-user-id",
            "user_groups": [],
            "resource_path": "/performance-analytics/counter-report/{proxy+}",
            "path_parts": [
                "",
                "performance-analytics",
                "counter-report",
                "v2",
                "test-line-id",
                "test-machine-id",
            ],
            "query_string_parameters": {"time_from": "*************", "time_to": "*************"},
            "multi_value_query_string_parameters": {},
            "headers": {},
            "body": {},
        }
    )


def test_lambda_handler_call_wrong_query_param(
    lambda_handler, counter_report_event, counter_report_performance
):
    report_event = counter_report_event([None, None])
    lambda_handler(report_event, "context")
    actual_result = lambda_handler(report_event, "context")

    assert (
        actual_result["body"]
        == '{"errorMessage": "Time range needs to be specified in the request.", "errorType": "BadRequest"}'
    )


@pytest.fixture
def wrong_args():
    return {
        "version": "2.0",
        "routeKey": "GET /v1/performance-analytics/counter-report/v2/{line_id}/{machine_id}",
        "rawPath": "/v1/performance-analytics/counter-report/v2/test-line-id/test-machine-id",
        "rawQueryString": "time_from=123123abc",
        "headers": {
            "accept": "application/json",
            "content-type": "application/json",
        },
        "queryStringParameters": {"time_from": "123123abc"},
        "requestContext": {
            "accountId": "test-account-id",
            "apiId": "test-api-id",
            "domainName": "test-domain.com",
            "http": {
                "method": "GET",
                "path": "/v1/performance-analytics/counter-report/v2/test-line-id/test-machine-id",
                "protocol": "HTTP/1.1",
                "sourceIp": "127.0.0.1",
                "userAgent": "test-agent"
            },
            "requestId": "test-request-id",
            "routeKey": "GET /v1/performance-analytics/counter-report/v2/{line_id}/{machine_id}",
            "stage": "test",
            "time": "01/Jan/2021:00:00:00 +0000",
            "timeEpoch": *************,
            "authorizer": {
                "claims": '{"scopes": ["performance-analytics"]}',
                "user": '{"userId": "test-user-id", "username": "test-user", "login": "test-login", "groups": []}',
                "account": '{"accountId": "test-account-id"}',
            },
        },
        "pathParameters": {
            "line_id": "test-line-id",
            "machine_id": "test-machine-id"
        },
        "isBase64Encoded": False,
    }


def test_catch_invalid_query_parameter(lambda_handler, wrong_args):
    from http import HTTPStatus

    actual_result = lambda_handler(wrong_args, "context")

    assert (
        actual_result["statusCode"] == HTTPStatus.BAD_REQUEST
    ), 'Invalid timestamp for "time_from"'
