from aws_lambda_powertools import Logger, Tracer
from fastapi import APIRouter, Path, Query, Request, Depends, HTTPException, status
from lib_cloud_sdk.util.common import validate_fast_api_timerange
from performance_analytics.fast_api.dependencies.s2a_properties import Share2ActProperties
from api.endpoints.utils import get_block_counter_report, get_machine_counter_report
from api.endpoints.models import CounterReportResponse

LOGGER = Logger()

router = APIRouter()

TRACER = Tracer()


# pylint: disable=too-many-positional-arguments, unused-argument
@router.get(
    "/v2/{line_id}",
    response_model=CounterReportResponse,
    response_model_exclude_none=True,
    tags=["counter-report"],
    operation_id="block_counter_report",
    dependencies=[Depends(validate_fast_api_timerange)],
)
@TRACER.capture_method(capture_response=False)
def block_counter_report(
    request: Request,
    properties: Share2ActProperties,
    line_id: str = Path(..., description="Line identifier"),
    time_from: int = Query(..., description="unix epoch timestamp in milliseconds"),
    time_to: int | None = Query(None, description="unix epoch timestamp in milliseconds "),
) -> CounterReportResponse:
    """
    Get block counter report for a specific line .
    """
    LOGGER.info(
        "Processing line counter report request",
        extra={
            "line_id": line_id,
            "time_from": time_from,
            "time_to": time_to,
            "account": properties.account,
        },
    )

    try:
        base_response = {
            "customer": properties.account,
            "eq_id": line_id,
            "time_from": time_from,
            "time_to": time_to,
        }

        result = get_block_counter_report(
            base_response=base_response,
            line_id=line_id,
            account=properties.account,
            time_from=time_from,
            time_to=time_to,
        )

        return result
    except HTTPException as http_excep:
        LOGGER.exception(
            http_excep,
            extra={
                "line_id": line_id,
                "account_id": properties.account,
            },
        )
        raise http_excep
    except Exception as excep:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        ) from excep


@router.get(
    "/v2/{line_id}/{machine_id}",
    response_model=CounterReportResponse,
    response_model_exclude_none=True,
    tags=["counter-report"],
    operation_id="machine_counter_report",
    dependencies=[Depends(validate_fast_api_timerange)],
)
@TRACER.capture_method(capture_response=False)
def machine_counter_report(
    request: Request,
    properties: Share2ActProperties,
    line_id: str = Path(..., description="Line identifier"),
    machine_id: str = Path(..., description="Machine identifier"),
    time_from: int = Query(..., description="unix epoch timestamp in milliseconds"),
    time_to: int | None = Query(None, description="unix epoch timestamp in milliseconds"),
) -> CounterReportResponse:
    """
    Get machine counter report for a specific machine.
    """
    LOGGER.info(
        "Processing machine counter report request",
        extra={
            "line_id": line_id,
            "machine_id": machine_id,
            "time_from": time_from,
            "time_to": time_to,
            "account": properties.account,
        },
    )

    try:
        base_response = {
            "customer": properties.account,
            "eq_id": machine_id,
            "time_from": time_from,
            "time_to": time_to,
        }

        result = get_machine_counter_report(
            base_response=base_response,
            machine_id=machine_id,
            account=properties.account,
            time_from=time_from,
            time_to=time_to,
        )

        return result
    except HTTPException as http_excep:
        LOGGER.exception(
            http_excep,
            extra={
                "line_id": line_id,
                "account_id": properties.account,
            },
        )
        raise http_excep
    except Exception as excep:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        ) from excep
